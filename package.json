{"name": "front-viber", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "16.2.12", "@angular/cdk": "16.2.12", "@angular/common": "16.2.12", "@angular/compiler": "16.2.12", "@angular/core": "16.2.12", "@angular/forms": "16.2.12", "@angular/platform-browser": "16.2.12", "@angular/platform-browser-dynamic": "16.2.12", "@angular/router": "16.2.12", "chart.js": "^3.9.1", "docx": "9.5.1", "file-saver": "2.0.5", "html2canvas": "1.4.1", "jspdf": "3.0.1", "ng2-charts": "4.0.1", "ngx-pagination": "6.0.3", "rxjs": "7.8.0", "tailwindcss": "3.4.17", "zone.js": "0.13.1"}, "devDependencies": {"@angular-devkit/build-angular": "16.2.12", "@angular/cli": "16.2.12", "@angular/compiler-cli": "16.2.12", "@types/file-saver": "2.0.7", "@types/jasmine": "4.3.0", "autoprefixer": "10.4.21", "jasmine-core": "4.6.0", "karma": "6.4.0", "karma-chrome-launcher": "3.2.0", "karma-coverage": "2.2.0", "karma-jasmine": "5.1.0", "karma-jasmine-html-reporter": "2.1.0", "postcss": "8.5.6", "tailwindcss": "3.4.17", "typescript": "5.1.3"}}