import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { CharacterService, Character } from '../../core/services/character.service';
import { Chart, ChartConfiguration, ChartType, registerables } from 'chart.js';

Chart.register(...registerables);

@Component({
  selector: 'app-analytics',
  templateUrl: './analytics.component.html',
  styleUrls: ['./analytics.component.css']
})
export class AnalyticsComponent implements OnInit {
  @ViewChild('chartCanvas', { static: true }) chartCanvas!: ElementRef<HTMLCanvasElement>;

  characters: Character[] = [];
  loading = true;
  error: string | null = null;

  selectedChartType: 'pie' | 'bar' | 'line' = 'pie';
  selectedDataType: 'gender' | 'species' | 'occupation' = 'gender';

  chart: Chart | null = null;

  constructor(private characterService: CharacterService) {}

  ngOnInit() {
    this.loadData();
  }

  loadData() {
    this.loading = true;
    this.characterService.loadCharacters().subscribe({
      next: (data) => {
        this.characters = data;
        this.loading = false;
        this.updateChart();
      },
      error: (err) => {
        this.error = err || 'Veri yüklenirken hata oluştu';
        this.loading = false;
      }
    });
  }

  onChartTypeChange() {
    this.updateChart();
  }

  onDataTypeChange() {
    this.updateChart();
  }

  updateChart() {
    if (this.chart) {
      this.chart.destroy();
    }

    const data = this.getChartData();
    const config = this.getChartConfig(data);

    this.chart = new Chart(this.chartCanvas.nativeElement, config);
  }

  private getChartData() {
    const counts: Record<string, number> = {};

    this.characters.forEach(char => {
      let key: string;
      switch (this.selectedDataType) {
        case 'gender':
          key = char.gender || 'Unknown';
          break;
        case 'species':
          key = char.species || 'Unknown';
          break;
        case 'occupation':
          key = char.occupation || 'Unknown';
          break;
        default:
          key = 'Unknown';
      }
      counts[key] = (counts[key] || 0) + 1;
    });

    const labels = Object.keys(counts);
    const values = Object.values(counts);

    return { labels, values };
  }

  private getChartConfig(data: { labels: string[], values: number[] }): ChartConfiguration {
    const colors = [
      '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
      '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'
    ];

    const baseConfig: ChartConfiguration = {
      type: this.selectedChartType as ChartType,
      data: {
        labels: data.labels,
        datasets: [{
          label: this.getDataTypeLabel(),
          data: data.values,
          backgroundColor: colors.slice(0, data.labels.length),
          borderColor: colors.slice(0, data.labels.length),
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: `${this.getDataTypeLabel()} Dağılımı`
          },
          legend: {
            display: this.selectedChartType === 'pie',
            position: 'bottom'
          }
        }
      }
    };

    if (this.selectedChartType === 'line') {
      (baseConfig.data!.datasets[0] as any).fill = false;
      (baseConfig.data!.datasets[0] as any).tension = 0.1;
    }

    return baseConfig;
  }

  private getDataTypeLabel(): string {
    switch (this.selectedDataType) {
      case 'gender': return 'Cinsiyet';
      case 'species': return 'Tür';
      case 'occupation': return 'Meslek';
      default: return 'Veri';
    }
  }

  getMaleCount(): number {
    return this.characters.filter(char => char.gender?.toLowerCase() === 'male').length;
  }

  getFemaleCount(): number {
    return this.characters.filter(char => char.gender?.toLowerCase() === 'female').length;
  }
}
