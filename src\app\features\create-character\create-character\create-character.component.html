<div class="container mx-auto px-4 py-8 max-w-4xl">
  <h1 class="text-3xl font-bold text-gray-800 mb-8 text-center"><PERSON><PERSON></h1>

  <!-- Progress Bar -->
  <div class="mb-8">
    <div class="flex justify-between items-center mb-2">
      <span class="text-sm font-medium text-gray-600">Adım {{ currentStep }} / {{ totalSteps }}</span>
      <span class="text-sm font-medium text-gray-600">{{ getProgress() | number:'1.0-0' }}%</span>
    </div>
    <div class="w-full bg-gray-200 rounded-full h-2">
      <div class="bg-blue-600 h-2 rounded-full transition-all duration-300"
           [style.width.%]="getProgress()"></div>
    </div>

    <!-- Step indicators -->
    <div class="flex justify-between mt-4">
      <div *ngFor="let step of [1, 2, 3]; let i = index"
           class="flex flex-col items-center cursor-pointer"
           (click)="goToStep(step)">
        <div class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors"
             [class]="currentStep >= step ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-600'">
          {{ step }}
        </div>
        <span class="text-xs mt-1 text-gray-600">
          {{ step === 1 ? 'Temel' : step === 2 ? 'Fiziksel' : 'Ek Bilgiler' }}
        </span>
      </div>
    </div>
  </div>

  <!-- Form Container -->
  <div class="bg-white rounded-lg shadow-lg p-8">

    <!-- Step 1: Basic Information -->
    <div *ngIf="currentStep === 1" class="space-y-6">
      <h2 class="text-2xl font-semibold text-gray-800 mb-6">Temel Bilgiler</h2>

      <form [formGroup]="step1Form" class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- First Name -->
          <div>
            <label for="firstName" class="block text-sm font-medium text-gray-700 mb-2">
              Ad <span class="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="firstName"
              formControlName="firstName"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              [class.border-red-500]="isFieldInvalid(step1Form, 'firstName')"
              placeholder="Karakterin adı">
            <div *ngIf="isFieldInvalid(step1Form, 'firstName')"
                 class="text-red-500 text-sm mt-1">
              {{ getFieldError(step1Form, 'firstName') }}
            </div>
          </div>

          <!-- Last Name -->
          <div>
            <label for="lastName" class="block text-sm font-medium text-gray-700 mb-2">
              Soyad <span class="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="lastName"
              formControlName="lastName"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              [class.border-red-500]="isFieldInvalid(step1Form, 'lastName')"
              placeholder="Karakterin soyadı">
            <div *ngIf="isFieldInvalid(step1Form, 'lastName')"
                 class="text-red-500 text-sm mt-1">
              {{ getFieldError(step1Form, 'lastName') }}
            </div>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Species -->
          <div>
            <label for="species" class="block text-sm font-medium text-gray-700 mb-2">
              Tür <span class="text-red-500">*</span>
            </label>
            <select
              id="species"
              formControlName="species"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              [class.border-red-500]="isFieldInvalid(step1Form, 'species')">
              <option value="">Tür seçin</option>
              <option *ngFor="let species of speciesOptions" [value]="species">{{ species }}</option>
            </select>
            <div *ngIf="isFieldInvalid(step1Form, 'species')"
                 class="text-red-500 text-sm mt-1">
              {{ getFieldError(step1Form, 'species') }}
            </div>
          </div>

          <!-- Occupation -->
          <div>
            <label for="occupation" class="block text-sm font-medium text-gray-700 mb-2">
              Meslek <span class="text-red-500">*</span>
            </label>
            <select
              id="occupation"
              formControlName="occupation"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              [class.border-red-500]="isFieldInvalid(step1Form, 'occupation')">
              <option value="">Meslek seçin</option>
              <option *ngFor="let occupation of occupationOptions" [value]="occupation">{{ occupation }}</option>
            </select>
            <div *ngIf="isFieldInvalid(step1Form, 'occupation')"
                 class="text-red-500 text-sm mt-1">
              {{ getFieldError(step1Form, 'occupation') }}
            </div>
          </div>
        </div>
      </form>
    </div>

    <!-- Step 2: Physical Characteristics -->
    <div *ngIf="currentStep === 2" class="space-y-6">
      <h2 class="text-2xl font-semibold text-gray-800 mb-6">Fiziksel Özellikler</h2>

      <form [formGroup]="step2Form" class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Age -->
          <div>
            <label for="age" class="block text-sm font-medium text-gray-700 mb-2">
              Yaş
              <span *ngIf="isAgeRequired()" class="text-red-500">*</span>
              <span *ngIf="!isAgeRequired()" class="text-gray-500">(İsteğe bağlı)</span>
            </label>
            <input
              type="number"
              id="age"
              formControlName="age"
              min="0"
              max="999"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              [class.border-red-500]="isFieldInvalid(step2Form, 'age')"
              placeholder="Yaş (0-999)">
            <div *ngIf="isFieldInvalid(step2Form, 'age')"
                 class="text-red-500 text-sm mt-1">
              {{ getFieldError(step2Form, 'age') }}
            </div>
          </div>

          <!-- Model Number (only for robots) -->
          <div *ngIf="shouldShowModelNumber()">
            <label for="modelNumber" class="block text-sm font-medium text-gray-700 mb-2">
              Model Numarası <span class="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="modelNumber"
              formControlName="modelNumber"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              [class.border-red-500]="isFieldInvalid(step2Form, 'modelNumber')"
              placeholder="Robot model numarası">
            <div *ngIf="isFieldInvalid(step2Form, 'modelNumber')"
                 class="text-red-500 text-sm mt-1">
              {{ getFieldError(step2Form, 'modelNumber') }}
            </div>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Eye Color -->
          <div>
            <label for="eyeColor" class="block text-sm font-medium text-gray-700 mb-2">
              Göz Rengi <span class="text-red-500">*</span>
            </label>
            <select
              id="eyeColor"
              formControlName="eyeColor"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              [class.border-red-500]="isFieldInvalid(step2Form, 'eyeColor')">
              <option value="">Göz rengi seçin</option>
              <option *ngFor="let color of eyeColorOptions" [value]="color">{{ color }}</option>
            </select>
            <div *ngIf="isFieldInvalid(step2Form, 'eyeColor')"
                 class="text-red-500 text-sm mt-1">
              {{ getFieldError(step2Form, 'eyeColor') }}
            </div>
          </div>

          <!-- Hair Color -->
          <div>
            <label for="hairColor" class="block text-sm font-medium text-gray-700 mb-2">
              Saç Rengi <span class="text-red-500">*</span>
            </label>
            <select
              id="hairColor"
              formControlName="hairColor"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              [class.border-red-500]="isFieldInvalid(step2Form, 'hairColor')">
              <option value="">Saç rengi seçin</option>
              <option *ngFor="let color of hairColorOptions" [value]="color">{{ color }}</option>
            </select>
            <div *ngIf="isFieldInvalid(step2Form, 'hairColor')"
                 class="text-red-500 text-sm mt-1">
              {{ getFieldError(step2Form, 'hairColor') }}
            </div>
          </div>
        </div>
      </form>
    </div>

    <!-- Step 3: Additional Information -->
    <div *ngIf="currentStep === 3" class="space-y-6">
      <h2 class="text-2xl font-semibold text-gray-800 mb-6">Ek Bilgiler ve Özet</h2>

      <form [formGroup]="step3Form" class="space-y-6">
        <!-- Skills -->
        <div>
          <div class="flex justify-between items-center mb-4">
            <label class="block text-sm font-medium text-gray-700">
              Yetkinlikler
            </label>
            <button
              type="button"
              (click)="addSkill()"
              class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors">
              + Yeni Yetenek Ekle
            </button>
          </div>

          <div *ngIf="skillsArray.length === 0" class="text-gray-500 text-sm italic">
            Henüz yetenek eklenmedi. "Yeni Yetenek Ekle" butonuna tıklayarak yetenek ekleyebilirsiniz.
          </div>

          <div *ngFor="let skill of skillsArray.controls; let i = index"
               class="mb-2">
            <div class="flex gap-2">
              <input
                type="text"
                [formControl]="$any(skill)"
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                [class.border-red-500]="isSkillInvalid(i)"
                placeholder="Yetenek adı">
              <button
                type="button"
                (click)="removeSkill(i)"
                class="bg-red-600 text-white px-3 py-2 rounded hover:bg-red-700 transition-colors">
                Sil
              </button>
            </div>
            <div *ngIf="isSkillInvalid(i)"
                 class="text-red-500 text-sm mt-1">
              {{ getSkillError(i) }}
            </div>
          </div>
        </div>

        <!-- Description -->
        <div>
          <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
            Açıklama
            <span class="text-gray-500">(Maksimum 500 karakter)</span>
          </label>
          <textarea
            id="description"
            formControlName="description"
            rows="4"
            maxlength="500"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            [class.border-red-500]="isFieldInvalid(step3Form, 'description')"
            placeholder="Karakter hakkında açıklama..."></textarea>
          <div class="flex justify-between mt-1">
            <div *ngIf="isFieldInvalid(step3Form, 'description')"
                 class="text-red-500 text-sm">
              {{ getFieldError(step3Form, 'description') }}
            </div>
            <div class="text-gray-500 text-sm">
              {{ step3Form.get('description')?.value?.length || 0 }}/500
            </div>
          </div>
        </div>

        <!-- Summary -->
        <div class="bg-gray-50 p-6 rounded-lg">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">Karakter Özeti</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div><strong>Ad:</strong> {{ step1Form.get('firstName')?.value }} {{ step1Form.get('lastName')?.value }}</div>
            <div><strong>Tür:</strong> {{ step1Form.get('species')?.value }}</div>
            <div><strong>Meslek:</strong> {{ step1Form.get('occupation')?.value }}</div>
            <div *ngIf="step2Form.get('age')?.value"><strong>Yaş:</strong> {{ step2Form.get('age')?.value }}</div>
            <div><strong>Göz Rengi:</strong> {{ step2Form.get('eyeColor')?.value }}</div>
            <div><strong>Saç Rengi:</strong> {{ step2Form.get('hairColor')?.value }}</div>
            <div *ngIf="shouldShowModelNumber() && step2Form.get('modelNumber')?.value">
              <strong>Model No:</strong> {{ step2Form.get('modelNumber')?.value }}
            </div>
            <div *ngIf="skillsArray.length > 0" class="md:col-span-2">
              <strong>Yetkinlikler:</strong> {{ skillsArray.value.join(', ') }}
            </div>
            <div *ngIf="step3Form.get('description')?.value" class="md:col-span-2">
              <strong>Açıklama:</strong> {{ step3Form.get('description')?.value }}
            </div>
          </div>
        </div>
      </form>
    </div>

    <!-- Navigation Buttons -->
    <div class="flex justify-between mt-8 pt-6 border-t border-gray-200">
      <button
        type="button"
        (click)="previousStep()"
        [disabled]="currentStep === 1"
        class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
        ← Geri
      </button>

      <div class="flex gap-3">
        <button
          *ngIf="currentStep < totalSteps"
          type="button"
          (click)="nextStep()"
          class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
          İleri →
        </button>

        <button
          *ngIf="currentStep === totalSteps"
          type="button"
          (click)="onSubmit()"
          class="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
          Karakteri Oluştur
        </button>
      </div>
    </div>
  </div>
</div>
