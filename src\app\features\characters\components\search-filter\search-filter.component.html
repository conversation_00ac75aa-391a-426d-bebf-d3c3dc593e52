<div class="search-filter-container p-6 bg-white rounded-lg shadow-md mb-6">
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
    <!-- Search Input -->
    <div class="lg:col-span-2">
      <label for="search" class="block text-sm font-medium text-gray-700 mb-2">
        Search Characters
      </label>
      <input
        id="search"
        type="text"
        [(ngModel)]="searchTerm"
        (input)="onSearchChange($event)"
        placeholder="Search by name, occupation..."
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      />
    </div>

    <!-- Gender Filter -->
    <div>
      <label for="gender" class="block text-sm font-medium text-gray-700 mb-2">
        Gender
      </label>
      <select
        id="gender"
        [(ngModel)]="genderFilter"
        (change)="onGenderFilterChange($event)"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      >
        <option value="">All Genders</option>
        <option *ngFor="let gender of genderOptions" [value]="gender">{{ gender }}</option>
      </select>
    </div>

    <!-- Species Filter -->
    <div>
      <label for="species" class="block text-sm font-medium text-gray-700 mb-2">
        Species
      </label>
      <select
        id="species"
        [(ngModel)]="speciesFilter"
        (change)="onSpeciesFilterChange($event)"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      >
        <option value="">All Species</option>
        <option *ngFor="let species of speciesOptions" [value]="species">{{ species }}</option>
      </select>
    </div>

    <!-- Sort Dropdown -->
    <div>
      <label for="sort" class="block text-sm font-medium text-gray-700 mb-2">
        Sort By
      </label>
      <select
        id="sort"
        [(ngModel)]="sortBy"
        (change)="onSortChange($event)"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      >
        <option *ngFor="let option of sortOptions" [value]="option.value">{{ option.label }}</option>
      </select>
    </div>
  </div>

  <!-- Clear Filters Button -->
  <div class="mt-4 flex justify-end">
    <button
      (click)="clearFilters()"
      class="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors duration-200"
    >
      Clear All Filters
    </button>
  </div>
</div>
