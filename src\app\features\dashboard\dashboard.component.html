<div class="container mx-auto px-4 py-8">
  <h1 class="text-3xl font-bold text-gray-800 mb-8">Dashboard</h1>

  <!-- Loading State -->
  <div *ngIf="loading()" class="space-y-6">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div *ngFor="let i of [1,2,3,4]" class="bg-white rounded-lg shadow-md p-6 animate-pulse">
        <div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
        <div class="h-8 bg-gray-200 rounded w-1/2"></div>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="!loading() && error()"
       class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
    {{ error() }}
  </div>

  <!-- Dashboard Content -->
  <div *ngIf="!loading() && !error()" class="space-y-8">
    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Total Characters -->
      <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-blue-500">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Toplam Karakter</p>
            <p class="text-3xl font-bold text-gray-900">{{ totalCharacters() }}</p>
          </div>
          <div class="text-blue-500 text-3xl">👥</div>
        </div>
      </div>

      <!-- Average Age -->
      <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-green-500">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Ortalama Yaş</p>
            <p class="text-3xl font-bold text-gray-900">{{ averageAge() }}</p>
          </div>
          <div class="text-green-500 text-3xl">📊</div>
        </div>
      </div>

      <!-- Most Common Species -->
      <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-purple-500">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">En Yaygın Tür</p>
            <p class="text-lg font-bold text-gray-900 truncate">{{ mostCommonSpecies() }}</p>
          </div>
          <div class="text-purple-500 text-3xl">🧬</div>
        </div>
      </div>

      <!-- Most Common Occupation -->
      <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-orange-500">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">En Yaygın Meslek</p>
            <p class="text-lg font-bold text-gray-900 truncate">{{ mostCommonOccupation() }}</p>
          </div>
          <div class="text-orange-500 text-3xl">💼</div>
        </div>
      </div>
    </div>

    <!-- Gender Distribution -->
    <div class="bg-white rounded-lg shadow-md p-6">
      <h2 class="text-xl font-bold text-gray-800 mb-6">Cinsiyet Dağılımı</h2>
      <div class="space-y-4">
        <!-- Male -->
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium text-gray-600">Erkek</span>
          <div class="flex items-center space-x-3 flex-1 ml-4">
            <div class="flex-1 bg-gray-200 rounded-full h-3">
              <div class="bg-blue-500 h-3 rounded-full transition-all duration-500"
                   [style.width.%]="genderDistribution().male"></div>
            </div>
            <span class="text-sm font-bold text-gray-900 w-12 text-right">{{ genderDistribution().male }}%</span>
          </div>
        </div>

        <!-- Female -->
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium text-gray-600">Kadın</span>
          <div class="flex items-center space-x-3 flex-1 ml-4">
            <div class="flex-1 bg-gray-200 rounded-full h-3">
              <div class="bg-pink-500 h-3 rounded-full transition-all duration-500"
                   [style.width.%]="genderDistribution().female"></div>
            </div>
            <span class="text-sm font-bold text-gray-900 w-12 text-right">{{ genderDistribution().female }}%</span>
          </div>
        </div>

        <!-- Other -->
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium text-gray-600">Diğer</span>
          <div class="flex items-center space-x-3 flex-1 ml-4">
            <div class="flex-1 bg-gray-200 rounded-full h-3">
              <div class="bg-gray-500 h-3 rounded-full transition-all duration-500"
                   [style.width.%]="genderDistribution().other"></div>
            </div>
            <span class="text-sm font-bold text-gray-900 w-12 text-right">{{ genderDistribution().other }}%</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>